#####################数据库连接配置######################
# ShardingSphere 启用配置，默认为 true
# 设置为 true 时启用 ShardingSphere JDBC 功能，使用分片数据源
# 设置为 false 时禁用 ShardingSphere JDBC 功能，使用标准数据源
# 默认值为 true，确保向后兼容性
cloudkey.shardingsphere.enabled=false

# ShardingSphere 数据源配置（当 cloudkey.shardingsphere.enabled=true 时使用）
# 业务数据源配置
#shardingsphere.datasource.business.url=**************************************************************************************************************
#shardingsphere.datasource.business.username=system
#shardingsphere.datasource.business.password=dev!@#4875
#shardingsphere.datasource.business.driver-class-name=org.postgresql.Driver

# 归档数据源配置（仅在 ShardingSphere 启用时使用）
#shardingsphere.datasource.archive.url=**********************************************************************************************************************
#shardingsphere.datasource.archive.username=system
#shardingsphere.datasource.archive.password=dev!@#4875
#shardingsphere.datasource.archive.driver-class-name=org.postgresql.Driver

# Spring Boot 标准数据源配置（当 cloudkey.shardingsphere.enabled=false 时使用）
# 如果未配置，将回退使用 shardingsphere.datasource.business.* 配置
# 注意：禁用 ShardingSphere 后，归档数据库功能将不可用，只能使用业务数据库
# 分表功能也将不可用，所有表将使用标准表名
spring.datasource.url=**************************************************************************************************************
spring.datasource.username=system
spring.datasource.password=dev!@#4875
spring.datasource.driver-class-name=com.kingbase8.Driver

# HikariCP 连接池配置
#spring.datasource.hikari.minimum-idle=5
#spring.datasource.hikari.maximum-pool-size=15
#spring.datasource.hikari.connection-timeout=30000
#spring.datasource.hikari.idle-timeout=60000
#spring.datasource.hikari.max-lifetime=1800000

#####   alpha 开发环境     #####
#shardingsphere.datasource.business.url=jdbc:mysql://**************:3306/cloudkey_alpha?useUnicode=true&characterEncoding=UTF-8
#shardingsphere.datasource.business.username=root
#shardingsphere.datasource.business.password=Netca@2019
#shardingsphere.datasource.archive.url=**************************************************************************************
#shardingsphere.datasource.archive.username=root
#shardingsphere.datasource.archive.password=Netca@2019

#####################redis连接配置######################
#spring.redis.host=**************
#spring.redis.port=6379
#spring.redis.password=123456

### alpha 开发环境 ### CK_Redis@2020
spring.redis.host=**************
spring.redis.port=6359
spring.redis.password=CK_Redis@2020


#连接间隔 心跳检测
spring.redis.pingConnectionInterval=1000
# 一般来说是不用配置的，Spring Cache 会根据依赖的包自行装配
spring.cache.type=redis
# 连接超时时间（毫秒）
spring.redis.timeout=10000
# Redis默认情况下有16个分片，这里配置具体使用的分片
spring.redis.database=0
# 连接池最大连接数（使用负值表示没有限制） 默认 8
spring.redis.lettuce.pool.max-active=8
# 连接池最大阻塞等待时间（使用负值表示没有限制） 默认 -1
spring.redis.lettuce.pool.max-wait=-1
# 连接池中的最大空闲连接 默认 8
spring.redis.lettuce.pool.max-idle=8
# 连接池中的最小空闲连接 默认 0
spring.redis.lettuce.pool.min-idle=0

# redisson 连接池的大小
spring.redisson.connection.pool.size=100

#####################redis连接配置######################

########与业务平台通讯软证书信息--配置#########
cloudkey.system.certkeystorepath=${catalina.home}/conf/test.jks
cloudkey.system.certstorepwd=123456
cloudkey.system.certstoretype=JKS
cloudkey.system.cert_entry=test
cloudkey.system.cert_pwd=123456
########与业务平台通讯软证书信息--配置#########

#######使用JKS############
cloudkey.system.enc_keystorepath=${catalina.home}/conf/test.jks
cloudkey.system.enc_storepwd=ENC(3+4RuVPjoXZmWUYjoAbqCQ==)
cloudkey.system.enc_storetype=JKS
cloudkey.system.enc_cert_entry=test
cloudkey.system.enc_cert_pwd=ENC(3+4RuVPjoXZmWUYjoAbqCQ==)
cloudkey.system.devicetype=1
cloudkey.system.decryptcert=MIICVzCCAcCgAwIBAgIEV6LRLjANBgkqhkiG9w0BAQUFADBvMQswCQYDVQQGEwJDTjESMBAGA1UECBMJR3Vhbmdkb25nMRIwEAYDVQQHEwlHdWFuZ3pob3UxDjAMBgNVBAoTBU5FVENBMQ4wDAYDVQQLEwVORVRDQTEYMBYGA1UEAwwP55yB6LSo55uR5rWL6K+VMCAXDTE2MDgwNDA1MjI1NFoYDzIxMTYwNzExMDUyMjU0WjBvMQswCQYDVQQGEwJDTjESMBAGA1UECBMJR3Vhbmdkb25nMRIwEAYDVQQHEwlHdWFuZ3pob3UxDjAMBgNVBAoTBU5FVENBMQ4wDAYDVQQLEwVORVRDQTEYMBYGA1UEAwwP55yB6LSo55uR5rWL6K+VMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDMx4UDmOgeV5ZUcSNf6qHiIdaqGqFMMpZ2vk36RM/KIJXUSZWDwu52iPf0ETTu7BQjVFUrGHQOofnJQx5nWDh76gMrPFbPfxemTW2qElxgNes3QQxYluBF/n0tatz8WhvCrQApbDOtAAtJUqKG2Ay9hWM/4E7I+M7jHoagCuhoIwIDAQABMA0GCSqGSIb3DQEBBQUAA4GBAJcWmiSLr/jLLyuKSW9eyjpHeAvo/8+haKDaGr09ebb1jUE5sJOwuKRpbV0boEnwltaPBAEFcPeKytMhtPj1Aq2gjqiersX4HATJRdQ2rmVvDqPXKZIZFyfT7zmaVEFIgmyoVRxuHWSvu2v3f1fGi1f2zwrTMfwOBQQe3GgECZIa
#######使用JKS############

#业务平台加密证书，用于加密管理员PIN
cloudkey.system.bpms_enc_admin_pin_cert=MIIDDzCCArSgAwIBAgILEJpiE+R21X51/aEwCgYIKoEcz1UBg3UwYjELMAkGA1UEBhMCQ04xJDAiBgNVBAoMG05FVENBIENlcnRpZmljYXRlIEF1dGhvcml0eTEtMCsGA1UEAwwkTkVUQ0EgU00yIFRFU1QwMSBhbmQgRXZhbHVhdGlvbiBDQTAxMB4XDTIwMDQzMDA4MDgyOFoXDTI1MDQzMDA4MDgyOFowZjELMAkGA1UEBhMCQ04xEjAQBgNVBAgMCUd1YW5nZG9uZzEPMA0GA1UEBwwG5bm/5beeMRswGQYDVQQKDBJQSU7noIHliqDlr4bor4HkuaYxFTATBgNVBAMMDDE5Mi4xNjguMC41OTBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABF9bVKx6lYKtZqDxPmDGnsBZsSppoY+Y9nUidcJYLmyaUXtxK1rjVFdPHgSv2iu41nXzTJs/QlH7FhNIhrKT1gKjggFLMIIBRzAfBgNVHSMEGDAWgBQMe+ticwN1+oxKJAz2jzshZX4X6TAdBgNVHQ4EFgQUJGjIsmt4iWKB1Zz0Ft39axhMWFYwawYDVR0gBGQwYjBgBgorBgEEAYGSSA0KMFIwUAYIKwYBBQUHAgEWRGh0dHA6Ly93d3cuY25jYS5uZXQvY3Mva25vd2xlZGdlL3doaXRlcGFwZXIvY3BzL25ldENBdGVzdGNlcnRjcHMucGRmMDMGA1UdHwQsMCowKKAmoCSGImh0dHA6Ly90ZXN0LmNuY2EubmV0L2NybC9TTTJDQS5jcmwwDAYDVR0TAQH/BAIwADAOBgNVHQ8BAf8EBAMCBLAwDwYDVR0RBAgwBocEwKgAOzA0BgorBgEEAYGSSAEOBCYMJGRlNzcyOGRiZTFjNmE0MDAxMTQ3YjdkYjM3ZmEzMDYzQFMwMjAKBggqgRzPVQGDdQNJADBGAiEA2SyfnDPuUjGmNlSC1bMip4QTK+xyXwlPfw9ubZdlURACIQCMqR5yV6TpPK5xzkJKqjXud0DEruUOvKbVNV/rLz2zIA==
######################人脸识别###########################
face_landmarks_model_path=${catalina.home}/conf/DataModels/landmarks_model.dat
face_dnn_model_path=${catalina.home}/conf/DataModels/dnn_model.dat
face_similarity_limit=0.55
######################人脸识别###########################

################短信实现类#########################
cloudkey.system.smstype=net.netca.common.notice.sender.sms.console.ConsoleSender
#cloudkey.system.smstype=net.netca.common.notice.sender.sms.mas10086.Mas10086Sender,net.netca.common.notice.sender.sms.console.ConsoleSender
################短信实现类#########################

################ 正则表达式配置 #########################
cloudkey.regexp.idcard_all=(^\\d{15}$)|(^\\d{17}(\\d|X|x)$)
cloudkey.regexp.idcard_15=^(\\d{6})(\\d{2})(\\d{2})(\\d{2})(\\d{3})$
cloudkey.regexp.idcard_18=^(\\d{6})(\\d{4})(\\d{2})(\\d{2})(\\d{3})([0-9]|X|x)$
cloudkey.regexp.hkIdCard=^[A-Z]{1,2}[0-9]{6}\\(?[0-9A]\\)?$
cloudkey.regexp.social_credit=^[0-9A-Z]+$
cloudkey.regexp.taxid=^\\**[A-Za-z0-9]+$
cloudkey.regexp.telphone=^[0-9]{1,4}-?[0-9]{5,10}$
cloudkey.regexp.mobilephone=^(13|14|15|16|17|18|19)\\d{9}$
cloudkey.regexp.email=^(([^<>()\\[\\]\\.,;:\\s@\"]+(\\.[^<>()\\[\\]\\.,;:\\s@\"]+)*)|(\".+\"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$
################ 正则表达式配置 #########################

cloudkey.system.contextPath=/cloudkeyserver
cloudkey.system.downcertschedule_open=false

#################事件证书pin码配置#######################
cloudkey.event.userPin=Netca@2006
cloudkey.event.adminPin=Netca@2006

################启用分割密钥认证配置#######################
cloudkey.system.separate.encKey=
cloudkey.system.jws.keystore.path=${catalina.home}/conf/jws.pfx
cloudkey.system.jws.keystore.password=12345678
################启用分割密钥认证配置#######################
#################腾讯云刷脸认证#######################
cloudkey.tencent.face.url=https://192.168.201.155:9000/ia/v1/api/face/compare
cloudkey.tencent.face.appId=cloudkey_app
cloudkey.tencent.face.appKey=6640193361039919
#################腾讯云刷脸认证#######################
#################电子数据统一存储#######################
cloudkey.edss.url=https://192.168.201.115:7777/edssweb
cloudkey.edss.appId=cloudkey_gyfy
cloudkey.edss.appKey=01D4001F43FE5BED1A0E23544F122995
#cloudkey.edss.projectId=
#################电子数据统一存储#######################
#################定时任务表达式#######################
# 定时检查进行证书密钥更新的定时任务，默认每天0点执行.
cloudkey.cron.scheduledUpdateCert=0 0 0 * * ?
#################定时任务表达式#######################
#################密码卡调用方式 (enableTransfer=false && enableExternalDevice=false 表示本地密码卡，enableTransfer=true && enableExternalDevice=true 表示更换密码卡，enableTransfer=false && enableExternalDevice=true 表示网络密码卡)#######################
# 是否开启更换密码卡，true为开 false为关
cloudkey.card.enableTransfer=false
#是否开启外部服务密码设备，true为开 false为关
cloudkey.card.enableExternalDevice=true
#是否开启网络调用密码卡管理的地址
cloudkey.card.externalDeviceBaseUrl=http://**************:7777/cloudkeycardmngr/
#是否开启网络调用密码卡管理的日志，true为开 false为关
cloudkey.card.enableNetcardmngrLog=false
#################密码卡调用方式#######################
#################定时任务表达式#######################
# 定时清空应用日志的定时任务，默认每个月4号 28号4点30分执行.
cloudkey.cron.scheduledAppLogTruncate=0 30 4 4,28 * ?
#################定时任务表达式#######################

#####################定时任务配置######################
# 定时任务启用配置，默认为 true
# 设置为 true 时加载对应的定时任务服务，设置为 false 时不加载
# 默认值均为 true，确保向后兼容性

# 审计应用日志定时任务（依赖 ShardingSphere 启用）
# 当 cloudkey.shardingsphere.enabled=false 时，此任务将自动禁用
cloudkey.schedule.auditAppLog.enabled=true

# 审计应用日志清理定时任务（依赖 ShardingSphere 启用）
# 当 cloudkey.shardingsphere.enabled=false 时，此任务将自动禁用
cloudkey.schedule.auditAppLogTruncate.enabled=true

# 业务协调签名API日志定时任务（依赖 ShardingSphere 启用）
# 当 cloudkey.shardingsphere.enabled=false 时，此任务将自动禁用
cloudkey.schedule.businessCoordSignApiLog.enabled=true

# 业务协调签名API日志清理定时任务（依赖 ShardingSphere 启用）
# 当 cloudkey.shardingsphere.enabled=false 时，此任务将自动禁用
cloudkey.schedule.businessCoordSignApiLogTruncate.enabled=true

# 注意：以上4个定时任务强依赖 ShardingSphere 功能，当 ShardingSphere 被禁用时，
# 这些任务将自动禁用，无论其单独的 enabled 配置如何设置
#
# 定时任务与 ShardingSphere 的依赖关系说明：
# 1. 审计应用日志定时任务 (AuditAppLogScheduleService) - 依赖 ShardingSphere 分表功能
# 2. 审计应用日志清理定时任务 (AuditAppLogTruncateScheduleService) - 依赖 ShardingSphere 分表功能
# 3. 业务协调签名API日志定时任务 (BusinessCoordSignApiLogScheduleService) - 依赖 ShardingSphere 分表功能
# 4. 业务协调签名API日志清理定时任务 (BusinessCoordSignApiLogTruncateScheduleService) - 依赖 ShardingSphere 分表功能
#
# 这些定时任务需要使用 ShardingSphere 的分表功能来处理大量日志数据，
# 当 cloudkey.shardingsphere.enabled=false 时，系统将自动禁用这些任务以避免运行时错误
#
# 配置组合效果：
# cloudkey.shardingsphere.enabled=true  + cloudkey.schedule.*.enabled=true  -> 任务启用
# cloudkey.shardingsphere.enabled=true  + cloudkey.schedule.*.enabled=false -> 任务禁用
# cloudkey.shardingsphere.enabled=false + cloudkey.schedule.*.enabled=true  -> 任务自动禁用（依赖不满足）
# cloudkey.shardingsphere.enabled=false + cloudkey.schedule.*.enabled=false -> 任务禁用
#####################定时任务配置######################

