package net.netca.cloudkeyserver.changjiangca.cmp;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.po.BusinessRequest;
import net.netca.cloudkey.base.po.ConfigProject;
import net.netca.cloudkey.base.service.BusinessRequestService;
import net.netca.cloudkey.base.service.ConfigProjectService;
import net.netca.cloudkeyserver.changjiangca.config.CJCAConfigManager;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.codec.cmp.P10CertReqMessageCodec;
import net.netca.sdk.codec.cmp.PollReqMessageCodec;
import net.netca.sdk.codec.cmp.RevocationReqMessageCodec;
import net.netca.sdk.constants.RevokeReasonEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.SingleCertReqContext;

import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.message.cmp.PollReqMessage;
import net.netca.sdk.message.cmp.RevocationReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import okhttp3.*;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;


/**
 * 默认的基于 netcarasdk 的 CmpClient 实现。
 */
@Slf4j
@Component
public class NetcaRaCmpClient implements CmpClient {

    @Autowired
    private CJCAConfigManager configManager;
    @Autowired
    private BusinessRequestService businessRequestService;
    @Autowired
    private ConfigProjectService configProjectService;

    @Override
    public CmpRespResult registerByP10(String businessReqId, P10CertReqMessage message) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        SingleCertReqContext ctx = new SingleCertReqContext(message);
        MessageEncoder<CmpMessage> enc = P10CertReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, message);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = buildCmpUrlByBusinessReqId(businessReqId);
        return sendAndDecode(url, reqData, cfg, ctx, P10CertReqMessageCodec::createDecoder);
    }

    @Override
    public CmpRespResult poll(String businessReqId, long certRequestId) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        PollReqMessage msg = PollReqMessage.defaultMessage()
                .toBuilder()
                .senderStr(cfg.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cfg.getCommunicationCert().getSubject().getLdapName())
                .certRequestId(certRequestId)
                .build();
        SingleCertReqContext ctx = new SingleCertReqContext(msg);
        MessageEncoder<CmpMessage> enc = PollReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, msg);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = buildCmpUrlByBusinessReqId(businessReqId);
        return sendAndDecode(url, reqData, cfg, ctx, PollReqMessageCodec::createDecoder);
    }

    @Override
    public CmpRespResult revoke(String businessReqId, String certSn, String issuer, RevokeReasonEnum reason) throws Exception {
        return revokeInternal(buildCmpUrlByBusinessReqId(businessReqId), certSn, issuer, reason);
    }

    @Override
    public CmpRespResult revoke(String businessReqId, String certSn, String issuer, String reasonString) throws Exception {
        RevokeReasonEnum reason = parseReason(reasonString);
        return revoke(businessReqId, certSn, issuer, reason);
    }
    // ========== 以 templateId 直接请求的接口实现（便于特殊场景） ==========
    public CmpRespResult registerByP10WithTemplateId(String templateId, P10CertReqMessage message) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        SingleCertReqContext ctx = new SingleCertReqContext(message);
        MessageEncoder<CmpMessage> enc = P10CertReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, message);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = configManager.buildCmpRequestUrl(templateId);
        return sendAndDecode(url, reqData, cfg, ctx, P10CertReqMessageCodec::createDecoder);
    }

    public CmpRespResult pollWithTemplateId(String templateId, long certRequestId) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        PollReqMessage msg = PollReqMessage.defaultMessage().toBuilder()
                .senderStr(cfg.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cfg.getCommunicationCert().getSubject().getLdapName())
                .certRequestId(certRequestId)
                .build();
        SingleCertReqContext ctx = new SingleCertReqContext(msg);
        MessageEncoder<CmpMessage> enc = PollReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, msg);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = configManager.buildCmpRequestUrl(templateId);
        return sendAndDecode(url, reqData, cfg, ctx, PollReqMessageCodec::createDecoder);
    }

    @Override
    public CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, RevokeReasonEnum reason) throws Exception {
        String url = configManager.buildCmpRequestUrl(templateId);
        return revokeInternal(url, certSn, issuer, reason);
    }

    @Override
    public CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, String reasonString) throws Exception {
        RevokeReasonEnum reason = parseReason(reasonString);
        return revokeWithTemplateId(templateId, certSn, issuer, reason);
    }

    private CmpRespResult revokeInternal(String url, String certSn, String issuer, RevokeReasonEnum reason) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        RevocationReqMessage msg = RevocationReqMessage.defaultMessage().toBuilder()
                .senderStr(cfg.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cfg.getCommunicationCert().getSubject().getLdapName())
                .certSn(certSn)
                .issuer(issuer)
                .reason(reason.getCode())
                .build();
        SingleCertReqContext ctx = new SingleCertReqContext(msg);
        MessageEncoder<CmpMessage> enc = RevocationReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, msg);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        return sendAndDecode(url, reqData, cfg, ctx, RevocationReqMessageCodec::createDecoder);
    }

    private RevokeReasonEnum parseReason(String reasonString) {
        if (reasonString == null || reasonString.trim().isEmpty()) {
            return RevokeReasonEnum.UNSPECIFIED;
        }
        String rs = reasonString.trim().toUpperCase();
        switch (rs) {
            case "UNSPECIFIED":
                return RevokeReasonEnum.UNSPECIFIED;
            case "KEY_COMPROMISE":
            case "KEY_LOST":
                return RevokeReasonEnum.KEY_COMPROMISE;
            case "CA_COMPROMISE":
                return RevokeReasonEnum.CA_COMPROMISE;
            case "AFFILIATION_CHANGED":
            case "CHANGE_OF_RELATION":
                return RevokeReasonEnum.AFFILIATION_CHANGED;
            case "SUPERSEDED":
            case "KEY_HAS_UPDATED":
                return RevokeReasonEnum.SUPERSEDED;
            case "CESSATION_OF_OPERATION":
            case "STOP_USING":
                return RevokeReasonEnum.CESSATION_OF_OPERATION;
            case "CERTIFICATE_HOLD":
                return RevokeReasonEnum.CERTIFICATE_HOLD;
            case "REMOVE_FROM_CRL":
                return RevokeReasonEnum.REMOVE_FROM_CRL;
            case "PRIVILEGE_WITHDRAWN":
                return RevokeReasonEnum.PRIVILEGE_WITHDRAWN;
            case "A_A_COMPROMISE":
            case "AA_COMPROMISE":
                return RevokeReasonEnum.A_A_COMPROMISE;
            default:
                return RevokeReasonEnum.UNSPECIFIED;
        }
    }


    // ========================= 私有辅助 =========================

    private String buildCmpUrlByBusinessReqId(String businessReqId) throws Exception {
        BusinessRequest br = businessRequestService.selectById(businessReqId);
        if (br == null) {
            throw new CJCAException("CJCA_CONFIG_ERROR", "找不到reqId对应的业务请求:" + businessReqId);
        }
        ConfigProject cp = configProjectService.selectById(br.getProjectId());
        if (cp == null) {
            throw new CJCAException("CJCA_CONFIG_ERROR", "找不到projectId对应的项目配置:" + br.getProjectId());
        }
        return configManager.buildCmpRequestUrl(cp.getBpmsCertTemplateId());
    }

    private byte[] httpPost(String url, String contentType, byte[] body) throws Exception {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(configManager.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(configManager.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(configManager.getReadTimeout(), TimeUnit.MILLISECONDS)
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.get(contentType), body))
                .build();
        try (Response resp = client.newCall(request).execute()) {
            if (resp.body() == null) {
                throw new CJCAException("CJCA_RESPONSE_ERROR", "响应体为空");
            }
            if (!resp.isSuccessful()) {
                throw new CJCAException("CJCA_HTTP_ERROR",
                        String.format("HTTP请求失败: %d %s", resp.code(), resp.message()));
            }
            return resp.body().bytes();
        }
    }

    private CmpRespResult sendAndDecode(String url, byte[] req,
                                        CmpMessageConfigManagement cfg,
                                        SingleCertReqContext ctx,
                                        java.util.function.Function<CmpMessageConfigManagement, MessageDecoder<PKIMessage>> decoderFn) throws Exception {
        log.debug("发送CMP请求到URL: {}, 请求数据大小: {} bytes", url, req.length);
        byte[] res = httpPost(url, "application/pkixcmp", req);
        PKIMessages respMsgs = PKIMessages.getInstance(ASN1Sequence.getInstance(res));
        MessageDecoder<PKIMessage> decoder = decoderFn.apply(cfg);
        decoder.decode(ctx, respMsgs.toPKIMessageArray()[0]);
        CmpRespResult result = ctx.getCmpRespResult();
        if (result == null) {
            throw new CJCAException("CJCA_DECODE_ERROR", "解码CMP响应失败，结果为空");
        }
        return result;
    }
}

