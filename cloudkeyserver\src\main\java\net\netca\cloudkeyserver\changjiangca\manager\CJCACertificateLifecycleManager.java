package net.netca.cloudkeyserver.changjiangca.manager;

import cn.hutool.core.util.IdUtil;
import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.po.AuthorityOperator;
import net.netca.cloudkey.base.po.BusinessCertAttribute;
import net.netca.cloudkey.base.po.BusinessUser;
import net.netca.cloudkey.base.po.ConfigProject;

import net.netca.cloudkey.base.util.CodecUtil;
import net.netca.cloudkey.base.util.DateUtil;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.crypto.CryptoUtil;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.*;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.req.RegisterRequest;
import net.netca.cloudkey.lifecycle.bpms.netcabpmsservice.pojo.resp.NetcaBpmsResponse;
import net.netca.cloudkeyserver.changjiangca.adapter.CJCAResponseAdapter;
import net.netca.cloudkeyserver.changjiangca.cmp.CmpClient;
import net.netca.cloudkeyserver.changjiangca.config.CJCAConfigManager;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import net.netca.cloudkeyserver.manager.AbstractCertificateLifecycleManager;

import net.netca.sdk.constants.RevokeReasonEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.UserInfo;
import net.netca.sdk.message.Validity;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;


/**
 * 长江CA证书生命周期管理器
 *
 * 基于netcarasdk实现与长江CA平台的对接，支持：
 * - 基于CMP协议的证书申请、查询、下载、注销
 * - 自动配置和热更新支持
 * - 完整的错误处理和日志记录
 * - 与现有业务逻辑的无缝集成
 * - 使用父类提供的通用密钥对管理功能
 *
 * 注意：
 * - 长江CA实现不支持管理员PIN解密功能，doDecryptAdministratorPin方法将抛出UnsupportedOperationException异常
 * - 密钥对更新和保存功能使用AbstractCertificateLifecycleManager中的通用实现
 * - 如需特殊的密钥对处理逻辑，可重写doUpdateSignKeyPairAndSaveEncKeyPair方法
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Slf4j
@Component("cjcaCertificateLifecycleManager")
@ConditionalOnClass(name = "net.netca.sdk.codec.MessageEncoder")
public class CJCACertificateLifecycleManager extends AbstractCertificateLifecycleManager {

    // ==================== 依赖注入 ====================

    @Autowired
    private CJCAConfigManager configManager;

    @Autowired
    private CJCAResponseAdapter responseAdapter;

    @Autowired
    private CmpClient cmpClient;


    // ==================== ICertificateLifecycleManager 接口方法实现 ====================


    // ==================== 抽象方法实现 ====================

    /**
     * 实现证书申请
     *
     * @param registerRequest 证书申请请求
     * @param opSignature 操作签名
     * @param businessUser 业务用户
     * @param configProject 项目配置
     * @param url 申请URL（长江CA实现中忽略此参数）
     * @return 业务平台响应
     * @throws Exception 申请过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doApplyCertificate(RegisterRequest registerRequest, String opSignature, BusinessUser businessUser, ConfigProject configProject, String url) throws Exception {
        log.info("开始长江CA证书申请，用户ID: {}, 项目ID: {}", businessUser.getId(), configProject.getId());

        try {
            // 1. 构建CMP配置管理对象
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();
            if (cmpConfig == null) {
                throw new CJCAException("CJCA_CONFIG_ERROR", "无法获取CMP配置管理对象");
            }

            // 2. 生成证书请求ID
            long certReqId = generateCertificateRequestId();

            log.debug("生成证书请求ID: {}", certReqId);

            // 3. 构建用户信息
            UserInfo userInfo = buildUserInfoForCmp(businessUser);

            // 4. 构建证书有效期
            Date now = DateUtil.getNow();
            Cert cert = registerRequest.getCert();
            Integer interval = cert.getInterval();
            if (cert.getEndTime() == null) {
                cert.setEndTime(DateUtil.dateAddMonth(now, interval));
            }

            Validity validity = Validity.builder()
                    .startTime(now)
                    .endTime(cert.getEndTime())
                    .build();

            // 6. 构建自定义扩展信息
            CustomFreeText customFreeText = CustomFreeText.builder()
                    .validity(validity)
                    .userInfo(userInfo)
                    .certReqId(certReqId)
                    .build();

            // 7. 获取P10证书请求数据
            String p10Base64 = cert.getP10();
            if (!StringUtils.hasText(p10Base64)) {
                throw new CJCAException("CJCA_P10_ERROR", "P10证书请求数据为空");
            }

            // 8. 构建P10证书申请消息
            P10CertReqMessage p10CertReqMessage = P10CertReqMessage.defaultMessage()
                    .toBuilder()
                    .senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName())
                    .recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName())
                    .p10Base64(p10Base64)
                    .customFreeText(customFreeText)
                    .certRequestId(certReqId)
                    .build();

            log.debug("构建P10证书申请消息完成，发送方: {}, 接收方: {}", p10CertReqMessage.getSenderStr(), p10CertReqMessage.getRecipientStr());

            // 9-12. 交给 CmpClient: 编码→发送→解码（无需 Manager 关注二进制与HTTP细节）
            CmpRespResult cmpRespResult = cmpClient.registerByP10WithTemplateId(configProject.getBpmsCertTemplateId(), p10CertReqMessage);

            // 13. 适配响应结果
            NetcaBpmsResponse response = responseAdapter.adaptCertificateApplicationResponse(cmpRespResult);

            log.info("长江CA证书申请完成，请求ID: {}, 状态: {}", certReqId, response.getStatus());
            return response;

        } catch (CJCAException e) {
            log.error("长江CA证书申请失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("长江CA证书申请过程中发生未知错误", e);
            throw new CJCAException("CJCA_APPLY_ERROR", "证书申请失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实现证书状态查询
     *
     * @param systemId 系统ID
     * @param requestId 请求ID
     * @return 业务平台响应
     * @throws Exception 查询过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doQueryCertificateStatus(String systemId, String requestId) throws Exception {
        log.info("开始长江CA证书状态查询，系统ID: {}, 请求ID: {}", systemId, requestId);
        try {
            CmpRespResult cmpRespResult = cmpClient.poll(requestId, Long.parseLong(requestId));
            NetcaBpmsResponse response = responseAdapter.adaptCertificateStatusResponse(cmpRespResult);
            log.info("长江CA证书状态查询完成，请求ID: {}, 状态: {}", requestId, response.getStatus());
            return response;
        } catch (Exception e) {
            log.error("长江CA证书状态查询过程中发生未知错误", e);
            throw new CJCAException("CJCA_QUERY_ERROR", "证书状态查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实现证书下载
     *
     * @param requestId 请求ID
     * @param systemId 系统ID
     * @return 业务平台响应
     * @throws Exception 下载过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doDownloadCertificate(String requestId, String systemId) throws Exception {
        log.info("开始长江CA证书下载，请求ID: {}, 系统ID: {}", requestId, systemId);
        try {
            CmpRespResult cmpRespResult = cmpClient.poll(requestId, Long.parseLong(requestId));
            NetcaBpmsResponse response = responseAdapter.adaptCertificateDownloadResponse(cmpRespResult);
            log.info("长江CA证书下载完成，请求ID: {}, 状态: {}", requestId, response.getStatus());
            return response;
        } catch (Exception e) {
            log.error("长江CA证书下载过程中发生未知错误", e);
            throw new CJCAException("CJCA_DOWNLOAD_ERROR", "证书下载失败: " + e.getMessage(), e);
        }
    }

    /**
     * 实现证书注销
     *
     * @param businessCertAttribute 证书属性
     * @param configProject 项目配置
     * @param authorityOperator 授权操作员
     * @return 业务平台响应
     * @throws Exception 注销过程中的异常
     */
    @Override
    protected NetcaBpmsResponse doRevokeCertificate(BusinessCertAttribute businessCertAttribute, ConfigProject configProject, AuthorityOperator authorityOperator) throws Exception {
        log.info("开始长江CA证书注销，证书ID: {}, 项目ID: {}", businessCertAttribute.getId(), configProject.getId());

        try {
            // 1. 构建CMP配置管理对象
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();

            // 2. 解析注销原因：从业务扩展信息或其他来源获取字符串原因（若无则默认 UNSPECIFIED）
            String revokeReasonStr = extractRevokeReasonString(businessCertAttribute, configProject, authorityOperator);
            RevokeReasonEnum revokeReasonEnum = parseRevokeReason(revokeReasonStr);

            // 3. 交给 CmpClient 统一处理（支持字符串或枚举两种方式）
            CmpRespResult cmpRespResult = cmpClient.revokeWithTemplateId(
                    configProject.getBpmsCertTemplateId(),
                    businessCertAttribute.getCertSn(),
                    "CN=CJCA",
                    revokeReasonEnum);
            NetcaBpmsResponse response = responseAdapter.adaptCertificateRevocationResponse(cmpRespResult);

            log.info("长江CA证书注销完成，证书ID: {}, 状态: {}", businessCertAttribute.getId(), response.getStatus());
            return response;

        } catch (Exception e) {
            log.error("长江CA证书注销过程中发生未知错误", e);
            throw new CJCAException("CJCA_REVOKE_ERROR", "证书注销失败: " + e.getMessage(), e);
        }
    }

    /**
     * 管理员PIN解密实现
     *
     * 长江CA平台不支持管理员PIN解密功能，此方法将抛出UnsupportedOperationException异常。
     * 长江CA平台使用不同的密钥保护机制，不依赖传统的管理员PIN解密功能。
     *
     * @param encryptedPin 加密的PIN
     * @param systemId 系统ID
     * @return 解密后的PIN
     * @throws UnsupportedOperationException 长江CA不支持此功能
     */
    @Override
    protected String doDecryptAdministratorPin(String encryptedPin, String systemId) {
        log.warn("长江CA平台不支持管理员PIN解密功能，系统ID: {}", systemId);
        throw new UnsupportedOperationException("长江CA平台不支持管理员PIN解密功能。长江CA使用不同的密钥保护机制，不依赖传统的管理员PIN解密。");
    }

    // ==================== 业务信息保存方法 ====================
    // saveBusinessInfoBy 方法已移动到 AbstractCertificateLifecycleManager 中作为通用实现
    // CJCACertificateLifecycleManager 直接使用父类的通用实现，无需重写

    // ==================== updateBusinessInfo 抽象策略方法实现 ====================

    // ==================== 长江CA平台特定实现 ====================

    /**
     * 覆盖证书消息主动查询：使用 netcarasdk 轮询长江CA，返回父类所需的 Map（certContent 为 DER 字节）。
     */
    @Override
    protected Map<String, Map<String, Object>> queryCertificateMessages(NetcaBpmsResponse bpmsResponse,
                                                                        String businessReqId) throws Exception {
        // 1. 通过公共轮询封装获取 CMP 响应
        CmpRespResult cmpRespResult = cmpClient.poll(businessReqId, Long.parseLong(bpmsResponse.getReqId()));

        // 2. 将 CmpRespResult 转成父类所需 Map（DER 字节）
        Map<String, Map<String, Object>> result = new HashMap<>();

        // 签名证书
        byte[] signDer = cmpRespResult.getSignCertDer();
        if (signDer != null && signDer.length > 0) {
            Map<String, Object> signMap = buildCertMessageMapFromDer(signDer);
            result.put("signCertMessage", signMap);
        }

        // 加密证书
        byte[] encDer = cmpRespResult.getEncCertDer();
        if (encDer != null && encDer.length > 0) {
            Map<String, Object> encMap = buildCertMessageMapFromDer(encDer);
            // 加密密钥对（Base64）
            if (cmpRespResult.getEncPrivateKey() != null && cmpRespResult.getEncPrivateKey().length > 0) {
                encMap.put("encKeyPair", CodecUtil.base64Encode(cmpRespResult.getEncPrivateKey()));
            }
            result.put("encCertMessage", encMap);
        }

        return result;
    }


    // 由 DER 证书构建父类所需 Map，certContent 为 DER 原始字节
    private Map<String, Object> buildCertMessageMapFromDer(byte[] der) throws Exception {
        Map<String, Object> map = new HashMap<>();
        net.netca.pki.Certificate cert = null;
        try {
            // 以 Base64(DER) 构造证书对象，方便解析序列号/有效期/主体等信息
            String base64Der = java.util.Base64.getEncoder().encodeToString(der);
            cert = new net.netca.pki.Certificate(base64Der);
            String certSn = cert.getSerialNumber();
            map.put("certSn", certSn == null ? null : certSn.toUpperCase());
            map.put("certContent", der);
            map.put("certValidityStart", cert.getValidityStart());
            map.put("certValidityEnd", cert.getValidityEnd());
            map.put("o", cert.getSubjectO());
            map.put("ou", cert.getSubjectOU());
            map.put("cn", cert.getSubjectCN());
            // 拇印：SHA256，Base64（内部计算常量使用全限定名以避免额外导入）
            map.put("computeThumbprintAlgo", net.netca.pki.Hash.SHA256);
            String thumb = CryptoUtil.base64Encode(cert.computeThumbprint(net.netca.pki.Hash.SHA256));
            map.put("certThumbprint", thumb);
            return map;
        } finally {
            net.netca.cloudkey.base.util.ReleaseHelper.getInstance().release(cert);
        }
    }

    /**
     * 提取注销原因的字符串表示。
     * 优先来源：
     * - 业务侧扩展信息（如后续在 BusinessRequest.memo 或其他字段中放入）
     * - 也可根据项目配置、操作员类型等推断（目前默认不做推断）。
     *
     * 目前未接入具体来源，返回 null 以使用默认 UNSPECIFIED。
     */
    private String extractRevokeReasonString(BusinessCertAttribute businessCertAttribute,
                                             ConfigProject configProject,
                                             AuthorityOperator authorityOperator) {
        return null; // 暂无外部来源时，走默认
    }

    /**
     * 将字符串映射为 CMP 的注销原因枚举。
     * 支持常见别名，未知/空返回 UNSPECIFIED。
     */
    private RevokeReasonEnum parseRevokeReason(String revokeReason) {
        if (!org.springframework.util.StringUtils.hasText(revokeReason)) {
            return RevokeReasonEnum.UNSPECIFIED;
        }
        String v = revokeReason.trim().toUpperCase();
        switch (v) {
            case "UNSPECIFIED":
                return RevokeReasonEnum.UNSPECIFIED;
            case "KEY_COMPROMISE":
            case "KEY_LOST":
                return RevokeReasonEnum.KEY_COMPROMISE;
            case "CA_COMPROMISE":
                return RevokeReasonEnum.CA_COMPROMISE;
            case "AFFILIATION_CHANGED":
            case "CHANGE_OF_RELATION":
                return RevokeReasonEnum.AFFILIATION_CHANGED;
            case "SUPERSEDED":
            case "KEY_HAS_UPDATED":
                return RevokeReasonEnum.SUPERSEDED;
            case "CESSATION_OF_OPERATION":
            case "STOP_USING":
                return RevokeReasonEnum.CESSATION_OF_OPERATION;
            case "CERTIFICATE_HOLD":
                return RevokeReasonEnum.CERTIFICATE_HOLD;
            case "REMOVE_FROM_CRL":
                return RevokeReasonEnum.REMOVE_FROM_CRL;
            case "PRIVILEGE_WITHDRAWN":
                return RevokeReasonEnum.PRIVILEGE_WITHDRAWN;
            case "A_A_COMPROMISE":
            case "AA_COMPROMISE":
                return RevokeReasonEnum.A_A_COMPROMISE;
            default:
                return RevokeReasonEnum.UNSPECIFIED;
        }
    }

    /**
     * 生成证书请求ID
     */
    private long generateCertificateRequestId() {
        return IdUtil.getSnowflakeNextId();
    }

    /**
     * 为CMP构建用户信息
     */
    private UserInfo buildUserInfoForCmp(BusinessUser businessUser) {
        // 使用builder模式创建UserInfo
        return UserInfo.builder()
                .name(businessUser.getName())
                .identityType(businessUser.getIdentityType())
                .identity(businessUser.getIdentity())
                .telephone(businessUser.getPhone())
                .address(businessUser.getOfficialResidence())
                .build();
    }

}
