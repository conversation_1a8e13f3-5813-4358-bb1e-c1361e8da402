package net.netca.cloudkeyserver.changjiangca.cmp;

import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.constants.RevokeReasonEnum;

/**
 * CmpClient
 *
 * 负责封装基于 netcarasdk 的 CMP 编码/发送/解码能力。
 * 提供：申请、轮询、注销等常用操作。
 */
public interface CmpClient {

    // ========== 以 businessReqId 定位模板的接口（常规场景） ==========

    CmpRespResult registerByP10(String businessReqId, P10CertReqMessage message) throws Exception;

    CmpRespResult poll(String businessReqId, long certRequestId) throws Exception;

    /**
     * 注销证书（保留原签名，向后兼容）。
     */
    CmpRespResult revoke(String businessReqId, String certSn, String issuer, RevokeReasonEnum reason) throws Exception;

    /**
     * 注销证书（重载）：支持传入字符串原因，组件内部解析为 RevokeReasonEnum。
     * 若解析失败，则回退为 UNSPECIFIED。
     */
    CmpRespResult revoke(String businessReqId, String certSn, String issuer, String reasonString) throws Exception;

    // ========== 以 templateId 直接请求的接口（适用于已有模板上下文的场景） ==========

    CmpRespResult registerByP10WithTemplateId(String templateId, P10CertReqMessage message) throws Exception;

    CmpRespResult pollWithTemplateId(String templateId, long certRequestId) throws Exception;

    CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, RevokeReasonEnum reason) throws Exception;

    /**
     * 注销证书（重载）：支持字符串原因。
     */
    CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, String reasonString) throws Exception;
}

