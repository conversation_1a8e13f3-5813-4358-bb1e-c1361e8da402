package net.netca.cloudkeyserver.changjiangca.service;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.po.BusinessRequest;
import net.netca.cloudkey.base.po.ConfigProject;
import net.netca.cloudkey.base.service.BusinessRequestService;
import net.netca.cloudkey.base.service.ConfigProjectService;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 长江CA模板解析服务
 * 
 * <p>该服务专门负责将业务请求ID转换为证书模板ID，遵循单一职责原则。</p>
 * <p>将数据查询逻辑从CmpClient中分离出来，符合依赖倒置原则。</p>
 * 
 * <h3>设计原则：</h3>
 * <ul>
 *   <li><strong>单一职责</strong>：专注于业务请求到模板的映射转换</li>
 *   <li><strong>依赖倒置</strong>：为上层提供抽象的模板解析能力</li>
 *   <li><strong>接口隔离</strong>：只暴露必要的模板解析接口</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @since 2.42.2
 */
@Slf4j
@Service
public class CJCATemplateResolver {

    @Autowired
    private BusinessRequestService businessRequestService;
    
    @Autowired
    private ConfigProjectService configProjectService;

    /**
     * 根据业务请求ID解析证书模板ID
     * 
     * <p>该方法封装了从业务请求到证书模板的查询逻辑，为CMP客户端提供清晰的抽象。</p>
     * 
     * @param businessReqId 业务请求ID
     * @return 证书模板ID
     * @throws CJCAException 当业务请求或项目配置不存在时抛出
     */
    public String resolveTemplateId(String businessReqId) throws CJCAException {
        log.debug("开始解析业务请求ID对应的模板ID: {}", businessReqId);
        
        try {
            // 1. 查询业务请求
            BusinessRequest businessRequest = businessRequestService.selectById(businessReqId);
            if (businessRequest == null) {
                throw new CJCAException("CJCA_CONFIG_ERROR", 
                    "找不到reqId对应的业务请求: " + businessReqId);
            }

            // 2. 查询项目配置
            String projectId = businessRequest.getProjectId();
            ConfigProject configProject = configProjectService.selectById(projectId);
            if (configProject == null) {
                throw new CJCAException("CJCA_CONFIG_ERROR", 
                    "找不到projectId对应的项目配置: " + projectId);
            }

            // 3. 获取模板ID
            String templateId = configProject.getBpmsCertTemplateId();
            log.debug("解析模板ID成功: {} -> {}", businessReqId, templateId);
            
            return templateId;

        } catch (CJCAException e) {
            log.error("解析模板ID失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("解析模板ID过程中发生未知错误", e);
            throw new CJCAException("CJCA_RESOLVE_ERROR", 
                "解析模板ID失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量解析模板ID（用于批量操作场景）
     * 
     * @param businessReqIds 业务请求ID列表
     * @return 模板ID列表，顺序与输入一致
     * @throws CJCAException 当任何一个业务请求解析失败时抛出
     */
    public java.util.List<String> resolveTemplateIds(java.util.List<String> businessReqIds) throws CJCAException {
        log.debug("开始批量解析模板ID，数量: {}", businessReqIds.size());
        
        java.util.List<String> templateIds = new java.util.ArrayList<>();
        for (String businessReqId : businessReqIds) {
            templateIds.add(resolveTemplateId(businessReqId));
        }
        
        log.debug("批量解析模板ID完成，数量: {}", templateIds.size());
        return templateIds;
    }
}
