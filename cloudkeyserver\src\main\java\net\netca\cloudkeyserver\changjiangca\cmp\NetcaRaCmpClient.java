package net.netca.cloudkeyserver.changjiangca.cmp;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkey.base.po.BusinessUser;
import net.netca.cloudkeyserver.changjiangca.config.CJCAConfigManager;
import net.netca.cloudkeyserver.changjiangca.exception.CJCAException;
import net.netca.cloudkeyserver.changjiangca.service.CJCATemplateResolver;
import net.netca.sdk.codec.MessageDecoder;
import net.netca.sdk.codec.MessageEncoder;
import net.netca.sdk.codec.cmp.P10CertReqMessageCodec;
import net.netca.sdk.codec.cmp.PollReqMessageCodec;
import net.netca.sdk.codec.cmp.RevocationReqMessageCodec;
import net.netca.sdk.constants.RevokeReasonEnum;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.entity.SingleCertReqContext;

import net.netca.sdk.message.cmp.CmpMessage;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.message.cmp.PollReqMessage;
import net.netca.sdk.message.cmp.RevocationReqMessage;
import net.netca.sdk.message.cmp.config.CmpMessageConfigManagement;
import net.netca.sdk.message.CustomFreeText;
import net.netca.sdk.message.UserInfo;
import net.netca.sdk.message.Validity;
import okhttp3.*;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.cmp.PKIMessage;
import org.bouncycastle.asn1.cmp.PKIMessages;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * 默认的基于 netcarasdk 的 CmpClient 实现。
 */
@Slf4j
@Component
public class NetcaRaCmpClient implements CmpClient {

    @Autowired
    private CJCAConfigManager configManager;

    @Autowired
    private CJCATemplateResolver templateResolver;

    @Override
    public CmpRespResult registerByP10(String businessReqId, P10CertReqMessage message) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        SingleCertReqContext ctx = new SingleCertReqContext(message);
        MessageEncoder<CmpMessage> enc = P10CertReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, message);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = buildCmpUrlByBusinessReqId(businessReqId);
        return sendAndDecode(url, reqData, cfg, ctx, P10CertReqMessageCodec::createDecoder);
    }

    @Override
    public CmpRespResult poll(String businessReqId, long certRequestId) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        PollReqMessage msg = PollReqMessage.defaultMessage()
                .toBuilder()
                .senderStr(cfg.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cfg.getCommunicationCert().getSubject().getLdapName())
                .certRequestId(certRequestId)
                .build();
        SingleCertReqContext ctx = new SingleCertReqContext(msg);
        MessageEncoder<CmpMessage> enc = PollReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, msg);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = buildCmpUrlByBusinessReqId(businessReqId);
        return sendAndDecode(url, reqData, cfg, ctx, PollReqMessageCodec::createDecoder);
    }

    @Override
    public CmpRespResult revoke(String businessReqId, String certSn, String issuer, RevokeReasonEnum reason)
            throws Exception {
        return revokeInternal(buildCmpUrlByBusinessReqId(businessReqId), certSn, issuer, reason);
    }

    @Override
    public CmpRespResult revoke(String businessReqId, String certSn, String issuer, String reasonString)
            throws Exception {
        RevokeReasonEnum reason = parseReason(reasonString);
        return revoke(businessReqId, certSn, issuer, reason);
    }

    // ========== 以 templateId 直接请求的接口实现（便于特殊场景） ==========
    public CmpRespResult registerByP10WithTemplateId(String templateId, P10CertReqMessage message) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        SingleCertReqContext ctx = new SingleCertReqContext(message);
        MessageEncoder<CmpMessage> enc = P10CertReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, message);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = configManager.buildCmpRequestUrl(templateId);
        return sendAndDecode(url, reqData, cfg, ctx, P10CertReqMessageCodec::createDecoder);
    }

    public CmpRespResult pollWithTemplateId(String templateId, long certRequestId) throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        PollReqMessage msg = PollReqMessage.defaultMessage().toBuilder()
                .senderStr(cfg.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cfg.getCommunicationCert().getSubject().getLdapName())
                .certRequestId(certRequestId)
                .build();
        SingleCertReqContext ctx = new SingleCertReqContext(msg);
        MessageEncoder<CmpMessage> enc = PollReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, msg);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        String url = configManager.buildCmpRequestUrl(templateId);
        return sendAndDecode(url, reqData, cfg, ctx, PollReqMessageCodec::createDecoder);
    }

    @Override
    public CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, RevokeReasonEnum reason)
            throws Exception {
        String url = configManager.buildCmpRequestUrl(templateId);
        return revokeInternal(url, certSn, issuer, reason);
    }

    @Override
    public CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, String reasonString)
            throws Exception {
        RevokeReasonEnum reason = parseReason(reasonString);
        return revokeWithTemplateId(templateId, certSn, issuer, reason);
    }

    // ========== 高级业务接口实现 ==========

    /**
     * 基于业务数据申请证书（高级接口实现）
     *
     * <p>
     * 该方法实现了CMP协议的完整处理流程，将Manager层从技术细节中解放出来。
     * </p>
     * <p>
     * 遵循单一职责原则：Client层专注于CMP协议技术实现。
     * </p>
     */
    @Override
    public CmpRespResult applyCertificateWithBusinessData(String templateId, String p10Base64,
            BusinessUser businessUser, Date startTime, Date endTime,
            long certRequestId) throws Exception {
        log.debug("开始基于业务数据申请证书，模板ID: {}, 用户: {}, 请求ID: {}",
                templateId, businessUser.getName(), certRequestId);

        try {
            // 1. 创建CMP配置管理对象（统一在Client层管理）
            CmpMessageConfigManagement cmpConfig = configManager.createCmpMessageConfigManagement();
            if (cmpConfig == null) {
                throw new CJCAException("CJCA_CONFIG_ERROR", "无法获取CMP配置管理对象");
            }

            // 2. 验证P10数据
            if (!StringUtils.hasText(p10Base64)) {
                throw new CJCAException("CJCA_P10_ERROR", "P10证书请求数据为空");
            }

            // 3. 构建用户信息（业务对象到CMP对象的转换）
            UserInfo userInfo = buildUserInfoFromBusinessUser(businessUser);

            // 4. 构建证书有效期
            Validity validity = Validity.builder()
                    .startTime(startTime)
                    .endTime(endTime)
                    .build();

            // 5. 构建自定义扩展信息
            CustomFreeText customFreeText = CustomFreeText.builder()
                    .validity(validity)
                    .userInfo(userInfo)
                    .certReqId(certRequestId)
                    .build();

            // 6. 构建P10证书申请消息（封装CMP协议细节）
            P10CertReqMessage p10CertReqMessage = P10CertReqMessage.defaultMessage()
                    .toBuilder()
                    .senderStr(cmpConfig.getThirdPartyServerCommCert().getSubject().getLdapName())
                    .recipientStr(cmpConfig.getCommunicationCert().getSubject().getLdapName())
                    .p10Base64(p10Base64)
                    .customFreeText(customFreeText)
                    .certRequestId(certRequestId)
                    .build();

            log.debug("构建P10证书申请消息完成，发送方: {}, 接收方: {}",
                    p10CertReqMessage.getSenderStr(), p10CertReqMessage.getRecipientStr());

            // 7. 执行CMP协议处理（复用现有的低级接口）
            CmpRespResult result = registerByP10WithTemplateId(templateId, p10CertReqMessage);

            log.debug("基于业务数据的证书申请完成，请求ID: {}", certRequestId);
            return result;

        } catch (CJCAException e) {
            log.error("基于业务数据申请证书失败: {}", e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("基于业务数据申请证书过程中发生未知错误", e);
            throw new CJCAException("CJCA_APPLY_ERROR", "证书申请失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将业务用户对象转换为CMP用户信息对象
     *
     * <p>
     * 该方法封装了业务对象到CMP协议对象的转换逻辑，遵循单一职责原则。
     * </p>
     *
     * @param businessUser 业务用户对象
     * @return CMP用户信息对象
     */
    private UserInfo buildUserInfoFromBusinessUser(BusinessUser businessUser) {
        return UserInfo.builder()
                .name(businessUser.getName())
                .identityType(businessUser.getIdentityType())
                .identity(businessUser.getIdentity())
                .telephone(businessUser.getPhone())
                .address(businessUser.getOfficialResidence())
                .build();
    }

    private CmpRespResult revokeInternal(String url, String certSn, String issuer, RevokeReasonEnum reason)
            throws Exception {
        CmpMessageConfigManagement cfg = configManager.createCmpMessageConfigManagement();
        RevocationReqMessage msg = RevocationReqMessage.defaultMessage().toBuilder()
                .senderStr(cfg.getThirdPartyServerCommCert().getSubject().getLdapName())
                .recipientStr(cfg.getCommunicationCert().getSubject().getLdapName())
                .certSn(certSn)
                .issuer(issuer)
                .reason(reason.getCode())
                .build();
        SingleCertReqContext ctx = new SingleCertReqContext(msg);
        MessageEncoder<CmpMessage> enc = RevocationReqMessageCodec.createEncoder(cfg);
        enc.encode(ctx, msg);
        byte[] reqData = ctx.getPKIMessages().getEncoded();
        return sendAndDecode(url, reqData, cfg, ctx, RevocationReqMessageCodec::createDecoder);
    }

    private RevokeReasonEnum parseReason(String reasonString) {
        if (reasonString == null || reasonString.trim().isEmpty()) {
            return RevokeReasonEnum.UNSPECIFIED;
        }
        String rs = reasonString.trim().toUpperCase();
        switch (rs) {
            case "UNSPECIFIED":
                return RevokeReasonEnum.UNSPECIFIED;
            case "KEY_COMPROMISE":
            case "KEY_LOST":
                return RevokeReasonEnum.KEY_COMPROMISE;
            case "CA_COMPROMISE":
                return RevokeReasonEnum.CA_COMPROMISE;
            case "AFFILIATION_CHANGED":
            case "CHANGE_OF_RELATION":
                return RevokeReasonEnum.AFFILIATION_CHANGED;
            case "SUPERSEDED":
            case "KEY_HAS_UPDATED":
                return RevokeReasonEnum.SUPERSEDED;
            case "CESSATION_OF_OPERATION":
            case "STOP_USING":
                return RevokeReasonEnum.CESSATION_OF_OPERATION;
            case "CERTIFICATE_HOLD":
                return RevokeReasonEnum.CERTIFICATE_HOLD;
            case "REMOVE_FROM_CRL":
                return RevokeReasonEnum.REMOVE_FROM_CRL;
            case "PRIVILEGE_WITHDRAWN":
                return RevokeReasonEnum.PRIVILEGE_WITHDRAWN;
            case "A_A_COMPROMISE":
            case "AA_COMPROMISE":
                return RevokeReasonEnum.A_A_COMPROMISE;
            default:
                return RevokeReasonEnum.UNSPECIFIED;
        }
    }

    // ========================= 私有辅助 =========================

    /**
     * 根据业务请求ID构建CMP请求URL
     *
     * <p>
     * 该方法使用模板解析服务来获取模板ID，遵循依赖倒置原则。
     * </p>
     *
     * @param businessReqId 业务请求ID
     * @return CMP请求URL
     * @throws Exception 当解析失败时抛出
     */
    private String buildCmpUrlByBusinessReqId(String businessReqId) throws Exception {
        // 使用专门的模板解析服务，符合单一职责原则
        String templateId = templateResolver.resolveTemplateId(businessReqId);
        return configManager.buildCmpRequestUrl(templateId);
    }

    private byte[] httpPost(String url, String contentType, byte[] body) throws Exception {
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(configManager.getConnectionTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(configManager.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(configManager.getReadTimeout(), TimeUnit.MILLISECONDS)
                .build();
        Request request = new Request.Builder()
                .url(url)
                .post(RequestBody.create(MediaType.get(contentType), body))
                .build();
        try (Response resp = client.newCall(request).execute()) {
            if (resp.body() == null) {
                throw new CJCAException("CJCA_RESPONSE_ERROR", "响应体为空");
            }
            if (!resp.isSuccessful()) {
                throw new CJCAException("CJCA_HTTP_ERROR",
                        String.format("HTTP请求失败: %d %s", resp.code(), resp.message()));
            }
            return resp.body().bytes();
        }
    }

    private CmpRespResult sendAndDecode(String url, byte[] req,
            CmpMessageConfigManagement cfg,
            SingleCertReqContext ctx,
            java.util.function.Function<CmpMessageConfigManagement, MessageDecoder<PKIMessage>> decoderFn)
            throws Exception {
        log.debug("发送CMP请求到URL: {}, 请求数据大小: {} bytes", url, req.length);
        byte[] res = httpPost(url, "application/pkixcmp", req);
        PKIMessages respMsgs = PKIMessages.getInstance(ASN1Sequence.getInstance(res));
        MessageDecoder<PKIMessage> decoder = decoderFn.apply(cfg);
        decoder.decode(ctx, respMsgs.toPKIMessageArray()[0]);
        CmpRespResult result = ctx.getCmpRespResult();
        if (result == null) {
            throw new CJCAException("CJCA_DECODE_ERROR", "解码CMP响应失败，结果为空");
        }
        return result;
    }
}
