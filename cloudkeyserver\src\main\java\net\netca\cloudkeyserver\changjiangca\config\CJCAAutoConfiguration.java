package net.netca.cloudkeyserver.changjiangca.config;

import lombok.extern.slf4j.Slf4j;
import net.netca.cloudkeyserver.changjiangca.adapter.CJCAResponseAdapter;
import net.netca.cloudkeyserver.changjiangca.manager.CJCACertificateLifecycleManager;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 长江CA自动配置类
 *
 * 实现Spring Boot的自动配置机制，当满足以下条件时自动配置长江CA相关组件：
 * 1. netcarasdk依赖存在于classpath中
 * 2. 配置项cloudkey.certificate.changjiang.enabled=true
 *
 * 自动配置的组件包括：
 * - CjcaConfigManager: 长江CA配置管理器
 * - CjcaResponseAdapter: 长江CA响应适配器
 * - ChangjiangCACertificateLifecycleManager: 长江CA证书生命周期管理器
 *
 * <AUTHOR> Team
 * @since 2.42.1
 */
@Slf4j
@Configuration
@ConditionalOnClass(name = "net.netca.sdk.codec.MessageEncoder")
@ConditionalOnProperty(
        prefix = "cloudkey.certificate.changjiang",
        name = "enabled",
        havingValue = "true"
)
public class CJCAAutoConfiguration {

    /**
     * 配置长江CA配置管理器Bean
     *
     * @return CjcaConfigManager实例
     */
    @Bean
    @ConditionalOnMissingBean
    public CJCAConfigManager cjcaConfigManager() {
        log.info("自动配置长江CA配置管理器");
        return new CJCAConfigManager();
    }

    /**
     * 配置长江CA响应适配器Bean
     *
     * @return CjcaResponseAdapter实例
     */
    @Bean
    @ConditionalOnMissingBean
    public CJCAResponseAdapter cjcaResponseAdapter() {
        log.info("自动配置长江CA响应适配器");
        return new CJCAResponseAdapter();
    }

    /**
     * 配置长江CA证书生命周期管理器Bean
     *
     * 注意：这个Bean的名称必须与CertificateLifecycleManagerFactory中的
     * CJCA_BEAN_NAME常量保持一致。
     *
     * @param configManager 配置管理器
     * @param responseAdapter 响应适配器
     * @return ChangjiangCACertificateLifecycleManager实例
     */
    @Bean("cjcaCertificateLifecycleManager")
    @ConditionalOnMissingBean(name = "cjcaCertificateLifecycleManager")
    public CJCACertificateLifecycleManager cjCACertificateLifecycleManager(
            CJCAConfigManager configManager,
            CJCAResponseAdapter responseAdapter) {

        log.info("自动配置长江CA证书生命周期管理器");

        // 创建实例
        CJCACertificateLifecycleManager manager = new CJCACertificateLifecycleManager();

        // 手动注入依赖（因为@Autowired在自动配置中可能不生效）
        try {
            // 使用反射设置私有字段
            java.lang.reflect.Field configField = CJCACertificateLifecycleManager.class
                    .getDeclaredField("configManager");
            configField.setAccessible(true);
            configField.set(manager, configManager);

            java.lang.reflect.Field adapterField = CJCACertificateLifecycleManager.class
                    .getDeclaredField("responseAdapter");
            adapterField.setAccessible(true);
            adapterField.set(manager, responseAdapter);

            log.debug("长江CA证书生命周期管理器依赖注入完成");

        } catch (Exception e) {
            log.warn("长江CA证书生命周期管理器依赖注入失败，将依赖Spring的@Autowired机制", e);
        }

        return manager;
    }

    /**
     * 配置信息Bean，用于监控和诊断
     *
     * @return 配置信息对象
     */
    @Bean
    @ConditionalOnMissingBean
    public CjcaConfigurationInfo cjcaConfigurationInfo() {
        log.info("自动配置长江CA配置信息Bean");
        return new CjcaConfigurationInfo();
    }

    /**
     * 长江CA配置信息类
     *
     * 提供配置状态查询和诊断功能
     */
    public static class CjcaConfigurationInfo {

        /**
         * 获取自动配置状态
         *
         * @return 配置状态信息
         */
        public String getConfigurationStatus() {
            return "长江CA自动配置已启用";
        }

        /**
         * 检查netcarasdk依赖是否可用
         *
         * @return true表示依赖可用，false表示不可用
         */
        public boolean isNetcaraSDKAvailable() {
            try {
                Class.forName("net.netca.sdk.codec.MessageEncoder");
                return true;
            } catch (ClassNotFoundException e) {
                return false;
            }
        }

        /**
         * 获取支持的功能列表
         *
         * @return 功能列表
         */
        public String[] getSupportedFeatures() {
            return new String[]{
                    "证书申请 (Certificate Application)",
                    "证书状态查询 (Certificate Status Query)",
                    "证书下载 (Certificate Download)",
                    "证书注销 (Certificate Revocation)",
                    "CMP协议支持 (CMP Protocol Support)",
                    "配置热更新 (Configuration Hot Reload)"
            };
        }

        /**
         * 获取版本信息
         *
         * @return 版本信息
         */
        public String getVersion() {
            return "2.42.1";
        }
    }
}
