package net.netca.cloudkeyserver.changjiangca.cmp;

import net.netca.cloudkey.base.po.BusinessUser;
import net.netca.sdk.entity.CmpRespResult;
import net.netca.sdk.message.cmp.P10CertReqMessage;
import net.netca.sdk.constants.RevokeReasonEnum;

import java.util.Date;

/**
 * CmpClient
 *
 * 负责封装基于 netcarasdk 的 CMP 编码/发送/解码能力。
 * 提供：申请、轮询、注销等常用操作。
 *
 * 设计原则：
 * - 低级接口：提供基础的CMP协议操作能力
 * - 高级接口：封装业务逻辑，简化Manager层调用
 * - 单一职责：专注于CMP协议的技术实现细节
 */
public interface CmpClient {

    // ========== 以 businessReqId 定位模板的接口（常规场景） ==========

    CmpRespResult registerByP10(String businessReqId, P10CertReqMessage message) throws Exception;

    CmpRespResult poll(String businessReqId, long certRequestId) throws Exception;

    /**
     * 注销证书（保留原签名，向后兼容）。
     */
    CmpRespResult revoke(String businessReqId, String certSn, String issuer, RevokeReasonEnum reason) throws Exception;

    /**
     * 注销证书（重载）：支持传入字符串原因，组件内部解析为 RevokeReasonEnum。
     * 若解析失败，则回退为 UNSPECIFIED。
     */
    CmpRespResult revoke(String businessReqId, String certSn, String issuer, String reasonString) throws Exception;

    // ========== 以 templateId 直接请求的接口（适用于已有模板上下文的场景） ==========

    CmpRespResult registerByP10WithTemplateId(String templateId, P10CertReqMessage message) throws Exception;

    CmpRespResult pollWithTemplateId(String templateId, long certRequestId) throws Exception;

    CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, RevokeReasonEnum reason)
            throws Exception;

    /**
     * 注销证书（重载）：支持字符串原因。
     */
    CmpRespResult revokeWithTemplateId(String templateId, String certSn, String issuer, String reasonString)
            throws Exception;

    // ========== 高级业务接口（封装CMP协议细节，简化Manager层调用） ==========

    /**
     * 基于业务数据申请证书（高级接口）
     *
     * <p>
     * 该方法封装了CMP协议的所有技术细节，包括：
     * </p>
     * <ul>
     * <li>CMP配置管理对象的创建和管理</li>
     * <li>用户信息到CMP UserInfo的转换</li>
     * <li>证书有效期的CMP Validity构建</li>
     * <li>自定义扩展信息的CustomFreeText构建</li>
     * <li>完整P10CertReqMessage的构建</li>
     * <li>CMP消息的编码、发送、解码</li>
     * </ul>
     *
     * <p>
     * Manager层只需要提供业务数据，无需了解CMP协议细节。
     * </p>
     *
     * @param templateId    证书模板ID
     * @param p10Base64     P10证书请求数据（Base64编码）
     * @param businessUser  业务用户信息
     * @param startTime     证书有效期开始时间
     * @param endTime       证书有效期结束时间
     * @param certRequestId 证书请求ID
     * @return CMP响应结果
     * @throws Exception 当申请过程中发生错误时抛出
     * @since 2.42.2
     */
    CmpRespResult applyCertificateWithBusinessData(
            String templateId,
            String p10Base64,
            BusinessUser businessUser,
            Date startTime,
            Date endTime,
            long certRequestId) throws Exception;
}
